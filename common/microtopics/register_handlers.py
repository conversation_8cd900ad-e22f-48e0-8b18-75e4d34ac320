import logging
from aiogram import F, Router
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from .handlers import (
    handle_microtopics_pagination_universal,
    show_detailed_microtopics_from_callback,
    show_summary_microtopics_from_callback,
    show_subject_detailed_microtopics_from_callback,
    show_subject_summary_microtopics_from_callback,
    show_month_entry_test_detailed_from_callback,
    show_month_entry_test_summary_from_callback,
    back_from_month_entry_test_microtopics_to_result,
    show_month_control_test_detailed_from_callback,
    show_month_control_test_summary_from_callback,
    back_from_month_control_test_microtopics_to_result,
    show_curator_month_entry_test_detailed_from_callback,
    show_curator_month_entry_test_summary_from_callback,
    back_from_curator_month_entry_test_microtopics_to_result
)


# Настройка логирования
logging.basicConfig(level=logging.INFO)


def register_microtopics_handlers(
    router: Router,
    states_group,
    role: str,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    subject_details_state,
    back_keyboard_func,
    back_from_image_func,            # Функция возврата из изображений микротем
    title: str,                      # Заголовок для микротем
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = True
):
    """
    Регистрация универсальных обработчиков микротем для роли

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний роли
        role: Роль пользователя ("student", "curator", "teacher", etc.)
        detailed_callback_prefix: Префикс для детальной пагинации (например: "student_microtopics_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "student_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        subject_details_state: Состояние выбора предмета
        back_keyboard_func: Функция для кнопки "Назад"
        back_from_image_func: Функция возврата из изображений микротем
        title: Заголовок для микротем
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # Основные обработчики отображения микротем
    @router.callback_query(subject_details_state, F.data.startswith("microtopics_detailed_"))
    async def show_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_detailed_microtopics | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_detailed_microtopics_from_callback(
            callback=callback,
            state=state,
            role=role,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            title=title,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика по микротемам",
            premium_check=premium_check,
            premium_feature="detailed_analytics"
        )

    @router.callback_query(subject_details_state, F.data.startswith("microtopics_summary_"))
    async def show_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_summary_microtopics | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_summary_microtopics_from_callback(
            callback=callback,
            state=state,
            role=role,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            title=title,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам",
            premium_check=premium_check,
            premium_feature="advanced_statistics"
        )

    # Обработчики пагинации для детальной статистики микротем
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_detailed_microtopics_pagination | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role=role
        )

    # Обработчики пагинации для сводки микротем
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_summary_microtopics_pagination | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role=role
        )

    # Универсальный обработчик возврата из изображений микротем (с фильтрацией по состояниям)
    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем"""
        current_state = await state.get_state()
        logging.info(f"🔥 СРАБОТАЛ ОБРАБОТЧИК РОЛИ {role.upper()}! | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {current_state}")
        print(f"🔥 СРАБОТАЛ ОБРАБОТЧИК РОЛИ {role.upper()}! | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {current_state}")
        await back_from_image_func(callback, state)

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем"""
        current_state = await state.get_state()
        logging.info(f"🔥 СРАБОТАЛ ОБРАБОТЧИК РОЛИ {role.upper()}! | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {current_state}")
        print(f"🔥 СРАБОТАЛ ОБРАБОТЧИК РОЛИ {role.upper()}! | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {current_state}")
        await back_from_image_func(callback, state)


def register_subject_microtopics_handlers(
    router: Router,
    states_group,
    role: str,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    subject_stats_state,
    back_keyboard_func,
    back_from_image_func,            # Функция возврата из изображений микротем предмета
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = False
):
    """
    Регистрация универсальных обработчиков микротем предмета

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний роли
        detailed_callback_prefix: Префикс для детальной пагинации (например: "manager_subject_microtopics_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "manager_subject_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        subject_stats_state: Состояние статистики предмета
        back_keyboard_func: Функция для кнопки "Назад"
        back_from_image_func: Функция возврата из изображений микротем предмета
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # Основные обработчики отображения микротем предмета
    @router.callback_query(subject_stats_state, F.data.startswith("subject_microtopics_detailed_"))
    async def show_subject_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_subject_detailed_microtopics | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_subject_detailed_microtopics_from_callback(
            callback=callback,
            state=state,
            role=role,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика по микротемам предмета",
            premium_check=premium_check
        )

    @router.callback_query(subject_stats_state, F.data.startswith("subject_microtopics_summary_"))
    async def show_subject_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_subject_summary_microtopics | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_subject_summary_microtopics_from_callback(
            callback=callback,
            state=state,
            role=role,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам предмета",
            premium_check=premium_check
        )

    # Обработчики пагинации для детальной статистики микротем предмета
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_subject_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_subject_detailed_microtopics_pagination | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role=role
        )

    # Обработчики пагинации для сводки микротем предмета
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_subject_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_subject_summary_microtopics_pagination | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role=role
        )

    # Универсальный обработчик возврата из изображений микротем предмета (с фильтрацией по состояниям)
    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_subject_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем предмета"""
        logging.info(f"ВЫЗОВ: back_from_subject_detailed_microtopics_image | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_image_func(callback, state, role)

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_subject_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем предмета"""
        logging.info(f"ВЫЗОВ: back_from_subject_summary_microtopics_image | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_image_func(callback, state, role)


def register_month_entry_test_microtopics_handlers(
    router: Router,
    states_group,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    result_state,
    back_keyboard_func,
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = False
):
    """
    Регистрация универсальных обработчиков микротем для входных тестов месяца

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний
        detailed_callback_prefix: Префикс для детальной пагинации (например: "student_month_entry_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "student_month_entry_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        result_state: Состояние результата теста
        back_keyboard_func: Функция для кнопки "Назад"
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # Основные обработчики отображения микротем входного теста месяца
    @router.callback_query(result_state, F.data.startswith("student_month_entry_detailed_"))
    async def show_month_entry_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_month_entry_detailed_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_month_entry_test_detailed_from_callback(
            callback=callback,
            state=state,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика входного теста месяца",
            premium_check=premium_check
        )

    @router.callback_query(result_state, F.data.startswith("student_month_entry_summary_"))
    async def show_month_entry_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_month_entry_summary_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_month_entry_test_summary_from_callback(
            callback=callback,
            state=state,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам входного теста",
            premium_check=premium_check
        )

    # Обработчики пагинации для детальной статистики микротем входного теста
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_month_entry_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_month_entry_detailed_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role="student"
        )

    # Обработчики пагинации для сводки микротем входного теста
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_month_entry_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_month_entry_summary_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role="student"
        )

    # Универсальный обработчик возврата из изображений микротем входного теста (с фильтрацией по состояниям)
    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_month_entry_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем входного теста"""
        logging.info(f"ВЫЗОВ: back_from_month_entry_detailed_microtopics_image | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_month_entry_test_microtopics_to_result(callback, state)

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_month_entry_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем входного теста"""
        logging.info(f"ВЫЗОВ: back_from_month_entry_summary_microtopics_image | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_month_entry_test_microtopics_to_result(callback, state)


def register_month_control_test_microtopics_handlers(
    router: Router,
    states_group,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    result_state,
    back_keyboard_func,
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = False
):
    """
    Регистрация универсальных обработчиков микротем для контрольных тестов месяца

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний
        detailed_callback_prefix: Префикс для детальной пагинации (например: "student_month_control_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "student_month_control_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        result_state: Состояние результата теста
        back_keyboard_func: Функция для кнопки "Назад"
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # Основные обработчики отображения микротем контрольного теста месяца
    @router.callback_query(result_state, F.data.startswith("student_month_control_detailed_"))
    async def show_month_control_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_month_control_detailed_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_month_control_test_detailed_from_callback(
            callback=callback,
            state=state,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика контрольного теста месяца",
            premium_check=premium_check
        )

    @router.callback_query(result_state, F.data.startswith("student_month_control_summary_"))
    async def show_month_control_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_month_control_summary_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_month_control_test_summary_from_callback(
            callback=callback,
            state=state,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам контрольного теста",
            premium_check=premium_check
        )

    # Обработчики пагинации для детальной статистики микротем контрольного теста
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_month_control_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_month_control_detailed_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role="student"
        )

    # Обработчики пагинации для сводки микротем контрольного теста
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_month_control_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_month_control_summary_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role="student"
        )

    # Универсальный обработчик возврата из изображений микротем контрольного теста (с фильтрацией по состояниям)
    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_month_control_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем контрольного теста"""
        logging.info(f"ВЫЗОВ: back_from_month_control_detailed_microtopics_image | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_month_control_test_microtopics_to_result(callback, state)

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_month_control_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем контрольного теста"""
        logging.info(f"ВЫЗОВ: back_from_month_control_summary_microtopics_image | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await back_from_month_control_test_microtopics_to_result(callback, state)


def register_curator_month_entry_test_microtopics_handlers(
    router: Router,
    states_group,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    result_state,
    back_keyboard_func,
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = False
):
    """
    Регистрация универсальных обработчиков микротем для входных тестов месяца куратора

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний
        detailed_callback_prefix: Префикс для детальной пагинации (например: "curator_month_entry_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "curator_month_entry_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        result_state: Состояние результата теста (не используется для куратора)
        back_keyboard_func: Функция для кнопки "Назад"
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # ЛОГИРОВАНИЕ: Показываем какие состояния регистрируются
    print(f"🎯 РЕГИСТРАЦИЯ КУРАТОР: detailed_state = {detailed_state}")
    print(f"🎯 РЕГИСТРАЦИЯ КУРАТОР: summary_state = {summary_state}")
    print(f"🎯 РЕГИСТРАЦИЯ КУРАТОР: result_state = {result_state}")

    # Добавляем общий обработчик для отладки (БЕЗ фильтра состояний)
    @router.callback_query(F.data == "back_from_microtopics_image")
    async def debug_back_from_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """ОТЛАДОЧНЫЙ обработчик для всех callback back_from_microtopics_image"""
        current_state = await state.get_state()
        print(f"🚨 ОТЛАДКА: Получен callback back_from_microtopics_image")
        print(f"🚨 ОТЛАДКА: Текущее состояние = {current_state}")
        print(f"🚨 ОТЛАДКА: User ID = {callback.from_user.id}")
        logging.info(f"🚨 ОТЛАДКА: callback back_from_microtopics_image, состояние={current_state}, user={callback.from_user.id}")

        # НЕ ВЫЗЫВАЕМ await callback.answer() - пусть другие обработчики тоже сработают

    # Основные обработчики отображения микротем входного теста месяца для куратора (С ФИЛЬТРОМ ПО СОСТОЯНИЯМ!)
    @router.callback_query(result_state, F.data.startswith("month_entry_detailed_"))
    async def show_curator_month_entry_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_curator_month_entry_detailed_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_curator_month_entry_test_detailed_from_callback(
            callback=callback,
            state=state,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика входного теста месяца",
            premium_check=premium_check
        )

    @router.callback_query(result_state, F.data.startswith("month_entry_summary_"))
    async def show_curator_month_entry_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_curator_month_entry_summary_microtopics | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_curator_month_entry_test_summary_from_callback(
            callback=callback,
            state=state,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам входного теста",
            premium_check=premium_check
        )

    # Обработчики пагинации для детальной статистики микротем входного теста куратора
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_curator_month_entry_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_curator_month_entry_detailed_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role="curator"
        )

    # Обработчики пагинации для сводки микротем входного теста куратора
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_curator_month_entry_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_curator_month_entry_summary_microtopics_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role="curator"
        )

    # Обработчики возврата из изображений микротем входного теста куратора (С фильтрацией по состояниям)
    @router.callback_query(detailed_state, F.data == "back_from_microtopics_image")
    async def back_from_curator_month_entry_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из детальной статистики микротем входного теста куратора"""
        current_state = await state.get_state()
        print(f"🔥 СРАБОТАЛ DETAILED ОБРАБОТЧИК КУРАТОРА! Состояние: {current_state}")
        logging.info(f"🔥 СРАБОТАЛ DETAILED ОБРАБОТЧИК КУРАТОРА! КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {current_state}")
        await back_from_curator_month_entry_test_microtopics_to_result(callback, state)

    @router.callback_query(summary_state, F.data == "back_from_microtopics_image")
    async def back_from_curator_month_entry_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из сводки микротем входного теста куратора"""
        current_state = await state.get_state()
        print(f"🔥 СРАБОТАЛ SUMMARY ОБРАБОТЧИК КУРАТОРА! Состояние: {current_state}")
        logging.info(f"🔥 СРАБОТАЛ SUMMARY ОБРАБОТЧИК КУРАТОРА! КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {current_state}")
        await back_from_curator_month_entry_test_microtopics_to_result(callback, state)

    # ДОПОЛНИТЕЛЬНЫЙ обработчик для состояния result_display (когда пользователь уже в результате теста)
    @router.callback_query(result_state, F.data == "back_from_microtopics_image")
    async def back_from_curator_month_entry_result_microtopics_image(callback: CallbackQuery, state: FSMContext):
        """Возврат из микротем к результату теста куратора"""
        current_state = await state.get_state()
        print(f"🔥 СРАБОТАЛ RESULT_DISPLAY ОБРАБОТЧИК КУРАТОРА! Состояние: {current_state}")
        logging.info(f"🔥 СРАБОТАЛ RESULT_DISPLAY ОБРАБОТЧИК КУРАТОРА! КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {current_state}")
        await back_from_curator_month_entry_test_microtopics_to_result(callback, state)



